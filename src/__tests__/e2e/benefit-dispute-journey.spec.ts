/**
 * Benefit Dispute Journey E2E Tests
 * Tests the complete benefit dispute workflow from user submission to admin resolution
 * and automatic benefit removal when multiple disputes are approved
 */

import { test, expect } from '@playwright/test'
import { signInUser, signInAdmin, waitForPageLoad, clearAuth } from './auth-helpers'
import { cleanupTestData } from './test-cleanup'
import { query } from '@/lib/local-db'

test.describe('Benefit Dispute Journey', () => {
  let testCompanyId: string
  let testBenefitId: string
  let testCompanyBenefitId: string
  let testUser1Id: string
  let testUser2Id: string

  test.beforeAll(async () => {
    // Set up test data for dispute journey
    await setupDisputeTestData()
  })

  test.beforeEach(async ({ page }) => {
    // Ensure clean state
    await clearAuth(page)
    // Add delay between tests to prevent rate limiting
    await page.waitForTimeout(5000)
  })

  test.afterAll(async () => {
    // Clean up test data
    await cleanupDisputeTestData()
  })

  test('Complete Benefit Dispute Journey - Two Disputes Lead to Benefit Removal', async ({ page }) => {
    console.log('🎯 Starting complete benefit dispute journey test')

    // Step 1: User 1 signs in and disputes the verified benefit
    console.log('👤 Step 1: User 1 disputes verified benefit')
    await signInUser(page, 'dispute-user1@disputetest.e2e')
    await page.goto('/dashboard')
    await waitForPageLoad(page)

    // Navigate to company page to find the benefit
    await page.goto(`/companies/dispute-test-corp`)
    await waitForPageLoad(page)

    // Find the verified benefit and click dispute button
    const benefitCard = page.locator('[data-testid="benefit-card"]').filter({ hasText: 'E2E Dispute Test Benefit' })
    await expect(benefitCard).toBeVisible()

    // Click on dispute/report button
    const disputeButton = benefitCard.locator('button').filter({ hasText: /Dispute|Report/ })
    await expect(disputeButton).toBeVisible()
    await disputeButton.click()

    // Fill in dispute form
    await page.fill('textarea[placeholder*="reason"]', 'This benefit is no longer offered by our company')
    await page.click('button:has-text("Submit Dispute")')

    // Wait for success message
    await expect(page.locator('text=Dispute submitted successfully')).toBeVisible()
    console.log('✅ User 1 dispute submitted successfully')

    // Step 2: Admin reviews and approves the first dispute
    console.log('👨‍💼 Step 2: Admin approves first dispute')
    await clearAuth(page)
    await signInAdmin(page, '<EMAIL>')
    
    await page.goto('/admin')
    await waitForPageLoad(page)

    // Navigate to disputes section
    await page.click('text=Benefit Disputes')
    await waitForPageLoad(page)

    // Find the pending dispute
    const firstDispute = page.locator('[data-testid="dispute-row"]').first()
    await expect(firstDispute).toBeVisible()
    await expect(firstDispute).toContainText('E2E Dispute Test Benefit')
    await expect(firstDispute).toContainText('pending')

    // Approve the first dispute
    const approveButton = firstDispute.locator('button:has-text("Approve")')
    await approveButton.click()

    // Add admin comment if modal appears
    const commentModal = page.locator('[data-testid="admin-comment-modal"]')
    if (await commentModal.isVisible()) {
      await page.fill('textarea[placeholder*="comment"]', 'First dispute approved - investigating benefit status')
      await page.click('button:has-text("Approve Dispute")')
    }

    // Wait for success message
    await expect(page.locator('text=Dispute approved successfully')).toBeVisible()
    console.log('✅ First dispute approved by admin')

    // Step 3: User 2 signs in and disputes the same benefit
    console.log('👤 Step 3: User 2 disputes same benefit')
    await clearAuth(page)
    await signInUser(page, 'dispute-user2@disputetest.e2e')
    
    await page.goto(`/companies/dispute-test-corp`)
    await waitForPageLoad(page)

    // Find the same benefit and dispute it
    const benefitCard2 = page.locator('[data-testid="benefit-card"]').filter({ hasText: 'E2E Dispute Test Benefit' })
    await expect(benefitCard2).toBeVisible()

    const disputeButton2 = benefitCard2.locator('button').filter({ hasText: /Dispute|Report/ })
    await expect(disputeButton2).toBeVisible()
    await disputeButton2.click()

    // Fill in second dispute form
    await page.fill('textarea[placeholder*="reason"]', 'I can confirm this benefit was discontinued last month')
    await page.click('button:has-text("Submit Dispute")')

    // Wait for success message
    await expect(page.locator('text=Dispute submitted successfully')).toBeVisible()
    console.log('✅ User 2 dispute submitted successfully')

    // Step 4: Admin approves the second dispute (should trigger benefit removal)
    console.log('👨‍💼 Step 4: Admin approves second dispute - should trigger benefit removal')
    await clearAuth(page)
    await signInAdmin(page, '<EMAIL>')
    
    await page.goto('/admin')
    await waitForPageLoad(page)

    // Navigate to disputes section
    await page.click('text=Benefit Disputes')
    await waitForPageLoad(page)

    // Find the second pending dispute
    const secondDispute = page.locator('[data-testid="dispute-row"]').filter({ hasText: 'pending' }).first()
    await expect(secondDispute).toBeVisible()
    await expect(secondDispute).toContainText('E2E Dispute Test Benefit')

    // Approve the second dispute
    const approveButton2 = secondDispute.locator('button:has-text("Approve")')
    await approveButton2.click()

    // Add admin comment if modal appears
    const commentModal2 = page.locator('[data-testid="admin-comment-modal"]')
    if (await commentModal2.isVisible()) {
      await page.fill('textarea[placeholder*="comment"]', 'Second dispute approved - benefit should be removed')
      await page.click('button:has-text("Approve Dispute")')
    }

    // Wait for success message that indicates benefit removal
    await expect(page.locator('text=Benefit automatically removed')).toBeVisible()
    console.log('✅ Second dispute approved - benefit removal triggered')

    // Step 5: Verify the benefit has been removed from the company
    console.log('🔍 Step 5: Verify benefit removal')
    await page.goto(`/companies/dispute-test-corp`)
    await waitForPageLoad(page)

    // The disputed benefit should no longer be visible
    const removedBenefit = page.locator('[data-testid="benefit-card"]').filter({ hasText: 'E2E Dispute Test Benefit' })
    await expect(removedBenefit).not.toBeVisible()
    console.log('✅ Benefit successfully removed from company')

    // Step 6: Verify dispute status in admin panel
    console.log('📊 Step 6: Verify dispute statuses in admin panel')
    await page.goto('/admin')
    await waitForPageLoad(page)
    await page.click('text=Benefit Disputes')
    await waitForPageLoad(page)

    // Both disputes should now show as approved
    const approvedDisputes = page.locator('[data-testid="dispute-row"]').filter({ hasText: 'approved' })
    await expect(approvedDisputes).toHaveCount(2)
    console.log('✅ Both disputes show as approved in admin panel')

    console.log('🎉 Complete benefit dispute journey test completed successfully!')
  })

  test('Single Dispute Approval Does Not Remove Benefit', async ({ page }) => {
    console.log('🎯 Testing single dispute approval (should not remove benefit)')

    // Create additional test benefit for this test
    await setupSingleDisputeTestData()

    // User disputes benefit
    await signInUser(page, 'dispute-user1@disputetest.e2e')
    await page.goto(`/companies/dispute-test-corp`)
    await waitForPageLoad(page)

    const benefitCard = page.locator('[data-testid="benefit-card"]').filter({ hasText: 'E2E Single Dispute Benefit' })
    await expect(benefitCard).toBeVisible()

    const disputeButton = benefitCard.locator('button').filter({ hasText: /Dispute|Report/ })
    await disputeButton.click()

    await page.fill('textarea[placeholder*="reason"]', 'Testing single dispute')
    await page.click('button:has-text("Submit Dispute")')
    await expect(page.locator('text=Dispute submitted successfully')).toBeVisible()

    // Admin approves single dispute
    await clearAuth(page)
    await signInAdmin(page, '<EMAIL>')
    await page.goto('/admin')
    await waitForPageLoad(page)
    await page.click('text=Benefit Disputes')
    await waitForPageLoad(page)

    const dispute = page.locator('[data-testid="dispute-row"]').filter({ hasText: 'E2E Single Dispute Benefit' }).first()
    await dispute.locator('button:has-text("Approve")').click()

    // Should NOT see benefit removal message
    await expect(page.locator('text=Dispute approved successfully')).toBeVisible()
    await expect(page.locator('text=Benefit automatically removed')).not.toBeVisible()

    // Verify benefit is still visible
    await page.goto(`/companies/dispute-test-corp`)
    await waitForPageLoad(page)
    const stillVisibleBenefit = page.locator('[data-testid="benefit-card"]').filter({ hasText: 'E2E Single Dispute Benefit' })
    await expect(stillVisibleBenefit).toBeVisible()

    console.log('✅ Single dispute approval correctly does not remove benefit')
  })

  // Helper functions for test data setup
  async function setupDisputeTestData() {
    console.log('🔧 Setting up dispute test data...')

    // Create test company
    const companyResult = await query(`
      INSERT INTO companies (name, domain, industry, size, description)
      VALUES ('Dispute Test Corp', 'disputetest.e2e', 'Technology', 'medium', 'Test company for dispute testing')
      ON CONFLICT (domain) DO UPDATE SET name = EXCLUDED.name
      RETURNING id
    `)
    testCompanyId = companyResult.rows[0].id

    // Create test benefit
    const benefitResult = await query(`
      INSERT INTO benefits (name, description, category_id, icon)
      VALUES ('E2E Dispute Test Benefit', 'Test benefit for dispute workflow', 
              (SELECT id FROM benefit_categories WHERE name = 'e2e_health' LIMIT 1), '🧪')
      ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description
      RETURNING id
    `)
    testBenefitId = benefitResult.rows[0].id

    // Create verified company benefit
    const companyBenefitResult = await query(`
      INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
      VALUES ($1, $2, true, 'admin')
      ON CONFLICT (company_id, benefit_id) DO UPDATE SET is_verified = true
      RETURNING id
    `, [testCompanyId, testBenefitId])
    testCompanyBenefitId = companyBenefitResult.rows[0].id

    // Create test users
    const user1Result = await query(`
      INSERT INTO users (email, first_name, last_name, role, payment_status, company_id, email_verified)
      VALUES ('dispute-user1@disputetest.e2e', 'Dispute', 'User1', 'user', 'free', $1, true)
      ON CONFLICT (email) DO UPDATE SET company_id = EXCLUDED.company_id
      RETURNING id
    `, [testCompanyId])
    testUser1Id = user1Result.rows[0].id

    const user2Result = await query(`
      INSERT INTO users (email, first_name, last_name, role, payment_status, company_id, email_verified)
      VALUES ('dispute-user2@disputetest.e2e', 'Dispute', 'User2', 'user', 'free', $1, true)
      ON CONFLICT (email) DO UPDATE SET company_id = EXCLUDED.company_id
      RETURNING id
    `, [testCompanyId])
    testUser2Id = user2Result.rows[0].id

    console.log('✅ Dispute test data setup complete')
  }

  async function setupSingleDisputeTestData() {
    // Create additional benefit for single dispute test
    const benefitResult = await query(`
      INSERT INTO benefits (name, description, category_id, icon)
      VALUES ('E2E Single Dispute Benefit', 'Test benefit for single dispute test', 
              (SELECT id FROM benefit_categories WHERE name = 'e2e_health' LIMIT 1), '🔬')
      ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description
      RETURNING id
    `)

    await query(`
      INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
      VALUES ($1, $2, true, 'admin')
      ON CONFLICT (company_id, benefit_id) DO NOTHING
    `, [testCompanyId, benefitResult.rows[0].id])
  }

  async function cleanupDisputeTestData() {
    console.log('🧹 Cleaning up dispute test data...')
    
    const cleanupQueries = [
      'DELETE FROM benefit_removal_disputes WHERE company_benefit_id IN (SELECT id FROM company_benefits WHERE company_id = $1)',
      'DELETE FROM company_benefits WHERE company_id = $1',
      'DELETE FROM users WHERE email LIKE \'%@disputetest.e2e\'',
      'DELETE FROM companies WHERE domain = \'disputetest.e2e\'',
      'DELETE FROM benefits WHERE name LIKE \'E2E%Dispute%Benefit\'',
    ]

    for (const sql of cleanupQueries) {
      try {
        await query(sql, [testCompanyId])
      } catch (error) {
        console.warn('Cleanup query failed (may be expected):', error)
      }
    }
    
    console.log('✅ Dispute test data cleanup complete')
  }
})
